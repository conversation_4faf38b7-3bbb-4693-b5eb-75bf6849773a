/**
 * EcoNet Products Page JavaScript
 * Advanced UI functionality for the products page
 */

// Base URL for assets
const asset_base_url = window.location.origin;

// Store original products HTML for reset functionality
let originalGridViewHTML = null;
let originalListViewHTML = null;
let currentView = 'grid'; // Default view is grid

// DOM Elements
const productsContainer = document.getElementById('productsContainer');
const gridViewContainer = document.getElementById('gridViewContainer');
const listViewContainer = document.getElementById('listViewContainer');
const searchField = document.getElementById('searchField');
const sortField = document.getElementById('sortField');
const categoryFilter = document.getElementById('categoryFilter');
const btnSort = document.getElementById('btnSort');
const btnClear = document.getElementById('btnClear');
const btnToggleView = document.getElementById('btnToggleView');

/**
 * Initialize the page
 */
function initProductsPage() {
    // Store original HTML for reset
    if (gridViewContainer) {
        originalGridViewHTML = gridViewContainer.innerHTML;
    }

    if (listViewContainer) {
        originalListViewHTML = listViewContainer.innerHTML;
    }

    // Add event listeners
    if (searchField) {
        searchField.addEventListener('input', filterProducts);
    }

    if (btnSort) {
        btnSort.addEventListener('click', sortProducts);
    }

    if (btnClear) {
        btnClear.addEventListener('click', resetProducts);
    }

    if (btnToggleView) {
        btnToggleView.addEventListener('click', toggleProductView);
    }

    // Add animation classes to product cards
    animateProductCards();

    // Add event listeners to Add to Cart buttons
    addCartButtonListeners();
}

/**
 * Filter products based on search input
 */
function filterProducts() {
    const searchValue = searchField.value.toLowerCase();
    const categoryValue = categoryFilter ? categoryFilter.value : 'all';

    // Filter grid view cards
    const gridCards = document.querySelectorAll('.grid-product-card');
    filterCardsByView(gridCards, searchValue, categoryValue);

    // Filter list view cards
    const listCards = document.querySelectorAll('.list-product-card');
    filterCardsByView(listCards, searchValue, categoryValue);
}

/**
 * Filter cards by view type
 */
function filterCardsByView(cards, searchValue, categoryValue) {
    cards.forEach(card => {
        const name = card.getAttribute('data-name').toLowerCase();
        const category = card.getAttribute('data-category').toLowerCase();

        // Filter by name and category if selected
        const nameMatch = name.includes(searchValue);
        const categoryMatch = categoryValue === 'all' || category === categoryValue;

        // Show/hide based on filters
        card.style.display = (nameMatch && categoryMatch) ? '' : 'none';

        // Add animation when cards are shown
        if (nameMatch && categoryMatch) {
            card.classList.add('fade-in');
            setTimeout(() => {
                card.classList.remove('fade-in');
            }, 500);
        }
    });
}

/**
 * Sort products by the selected field
 */
function sortProducts() {
    const sortValue = sortField.value;

    // Sort grid view
    sortCardsByView('.grid-product-card', gridViewContainer, sortValue);

    // Sort list view
    sortCardsByView('.list-product-card', listViewContainer, sortValue);
}

/**
 * Sort cards by view type
 */
function sortCardsByView(cardSelector, container, sortValue) {
    const cards = Array.from(document.querySelectorAll(cardSelector));

    cards.sort((a, b) => {
        if (sortValue === 'name') {
            const aValue = a.getAttribute('data-name').toLowerCase();
            const bValue = b.getAttribute('data-name').toLowerCase();
            return aValue.localeCompare(bValue);
        } else if (sortValue === 'price-low') {
            const aValue = parseFloat(a.getAttribute('data-price'));
            const bValue = parseFloat(b.getAttribute('data-price'));
            return aValue - bValue;
        } else if (sortValue === 'price-high') {
            const aValue = parseFloat(a.getAttribute('data-price'));
            const bValue = parseFloat(b.getAttribute('data-price'));
            return bValue - aValue;
        }
    });

    // Clear container and append sorted cards
    container.innerHTML = '';
    cards.forEach((card, index) => {
        // Add delay for staggered animation
        setTimeout(() => {
            container.appendChild(card);
            card.classList.add('fade-in');
            setTimeout(() => {
                card.classList.remove('fade-in');
            }, 500);
        }, index * 50);
    });

    // Re-add event listeners to Add to Cart buttons
    addCartButtonListeners();
}

/**
 * Reset products to original state
 */
function resetProducts() {
    if (originalGridViewHTML && gridViewContainer) {
        gridViewContainer.innerHTML = originalGridViewHTML;
    }

    if (originalListViewHTML && listViewContainer) {
        listViewContainer.innerHTML = originalListViewHTML;
    }

    // Reset filters
    if (searchField) searchField.value = '';
    if (sortField) sortField.value = 'name';
    if (categoryFilter) categoryFilter.value = 'all';

    // Animate cards
    animateProductCards();

    // Re-add event listeners to Add to Cart buttons
    addCartButtonListeners();
}

/**
 * Toggle between grid and list view
 */
function toggleProductView() {
    if (gridViewContainer && listViewContainer) {
        if (currentView === 'grid') {
            // Switch to list view
            gridViewContainer.style.display = 'none';
            listViewContainer.style.display = 'block';
            btnToggleView.innerHTML = '<i class="ri-grid-line"></i> Grid View';
            currentView = 'list';
        } else {
            // Switch to grid view
            gridViewContainer.style.display = 'grid';
            listViewContainer.style.display = 'none';
            btnToggleView.innerHTML = '<i class="ri-list-check"></i> List View';
            currentView = 'grid';
        }

        // Animate the newly visible cards
        animateProductCards();
    }
}

/**
 * Add event listeners to all buttons
 */
function addCartButtonListeners() {
    // Add listeners to grid view cart buttons
    document.querySelectorAll('.grid-btn-add-cart').forEach(button => {
        button.addEventListener('click', handleAddToCart);
    });

    // Add listeners to list view cart buttons
    document.querySelectorAll('.list-btn-add-cart').forEach(button => {
        button.addEventListener('click', handleAddToCart);
    });

    // Add listeners to QR code icon buttons
    document.querySelectorAll('.grid-qr-icon-btn').forEach(button => {
        button.addEventListener('click', handleQrCodeClick);
    });

    // Add listener to close QR dialog button
    const closeQrButton = document.getElementById('closeQrDialog');
    if (closeQrButton) {
        closeQrButton.addEventListener('click', closeQrDialog);
    }

    // Close dialog when clicking outside of it
    const qrDialog = document.getElementById('qrCodeDialog');
    if (qrDialog) {
        qrDialog.addEventListener('click', function(event) {
            if (event.target === qrDialog) {
                closeQrDialog();
            }
        });
    }

    // Add listeners to product images in list view
    document.querySelectorAll('.list-product-image').forEach(imageContainer => {
        imageContainer.addEventListener('click', handleProductImageClick);
    });

    // Add listeners to product images in grid view
    document.querySelectorAll('.grid-product-image').forEach(imageContainer => {
        imageContainer.addEventListener('click', handleProductImageClick);
    });

    // Add listener to close product image dialog button
    const closeImageButton = document.querySelector('.product-image-dialog-close');
    if (closeImageButton) {
        closeImageButton.addEventListener('click', closeProductImageDialog);
    }

    // Close product image dialog when clicking outside of it
    const imageDialog = document.getElementById('productImageDialog');
    if (imageDialog) {
        imageDialog.addEventListener('click', function(event) {
            if (event.target === imageDialog) {
                closeProductImageDialog();
            }
        });
    }
}

/**
 * Handle QR code button click
 */
function handleQrCodeClick(event) {
    const button = event.currentTarget;
    const productId = button.dataset.productId;
    const productName = button.dataset.productName;

    // Set the QR code image source
    const qrImage = document.getElementById('qrCodeImage');
    if (qrImage) {
        qrImage.src = `${asset_base_url}/uploads/qrcode/produit-${productId}.png`;
    }

    // Set the dialog title
    const dialogTitle = document.getElementById('qrDialogTitle');
    if (dialogTitle) {
        dialogTitle.textContent = `QR Code for ${productName}`;
    }

    // Show the dialog
    const dialog = document.getElementById('qrCodeDialog');
    if (dialog) {
        dialog.classList.add('show');
        // Prevent scrolling on the body
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Close QR code dialog
 */
function closeQrDialog() {
    const dialog = document.getElementById('qrCodeDialog');
    if (dialog) {
        dialog.classList.remove('show');
        // Restore scrolling
        document.body.style.overflow = '';
    }
}

/**
 * Handle Add to Cart button click
 */
function handleAddToCart(event) {
    const button = event.currentTarget;
    const product = {
        id: button.dataset.id,
        name: button.dataset.name,
        price: parseFloat(button.dataset.price),
        image: button.dataset.image,
        category: button.dataset.category,
        quantity: 1
    };

    // Add to cart in localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    cart.push(product);
    localStorage.setItem('cart', JSON.stringify(cart));

    // Show success feedback
    showAddToCartFeedback(button, product.name);
}

/**
 * Show visual feedback when product is added to cart
 */
function showAddToCartFeedback(button, productName) {
    // Store original button text
    const originalText = button.innerHTML;
    const originalBgColor = button.style.backgroundColor;

    // Change button to success state
    button.innerHTML = '<i class="ri-check-line"></i> Added!';
    button.style.backgroundColor = '#28a745';

    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'toast-notification';
    toast.innerHTML = `
        <div class="toast-icon"><i class="ri-check-line"></i></div>
        <div class="toast-content">
            <p><strong>${productName}</strong> added to cart</p>
        </div>
    `;
    document.body.appendChild(toast);

    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // Reset button after delay
    setTimeout(() => {
        button.innerHTML = originalText;
        button.style.backgroundColor = originalBgColor;
    }, 2000);

    // Remove toast after delay
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

/**
 * Animate product cards on page load
 */
function animateProductCards() {
    // Animate grid cards if grid view is active
    if (currentView === 'grid') {
        const gridCards = document.querySelectorAll('.grid-product-card');
        animateCards(gridCards);
    } else {
        // Animate list cards if list view is active
        const listCards = document.querySelectorAll('.list-product-card');
        animateCards(listCards);
    }
}

/**
 * Animate cards with staggered delay
 */
function animateCards(cards) {
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('fade-in');
            setTimeout(() => {
                card.classList.remove('fade-in');
            }, 500);
        }, index * 100);
    });
}

/**
 * Handle product image click in list view
 */
function handleProductImageClick(event) {
    // Find the image element inside the container
    const imageContainer = event.currentTarget;
    const imageElement = imageContainer.querySelector('img');

    // If there's no image (placeholder is shown), don't open dialog
    if (!imageElement) return;

    // Get the image source
    const imageSrc = imageElement.src;
    const imageAlt = imageElement.alt || 'Product Image';

    // Set the dialog image source
    const dialogImage = document.getElementById('productImageDialogImg');
    if (dialogImage) {
        dialogImage.src = imageSrc;
        dialogImage.alt = imageAlt;
    }

    // Show the dialog
    const dialog = document.getElementById('productImageDialog');
    if (dialog) {
        dialog.classList.add('active');
        // Prevent scrolling on the body
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Close product image dialog
 */
function closeProductImageDialog() {
    const dialog = document.getElementById('productImageDialog');
    if (dialog) {
        dialog.classList.remove('active');
        // Restore scrolling
        document.body.style.overflow = '';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initProductsPage);
