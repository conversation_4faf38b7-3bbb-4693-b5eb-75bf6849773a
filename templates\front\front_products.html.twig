{% extends 'front/base.html.twig' %}

{% block stylesheets %}
    {{ parent() }}
    <link rel="stylesheet" href="{{ asset('front/css/products.css') }}">
{% endblock %}

{% block content %}
<div class="container-fluid position-relative p-0">
    {% include 'front/includes/navbar.html.twig' %}
    <div class="container-fluid bg-breadcrumb-products">
        <div class="container text-center py-5" style="max-width: 900px">
            <h4 class="text-white display-4 mb-4 wow fadeInDown" data-wow-delay="0.1s">Eco-Friendly Products</h4>
            <ol class="breadcrumb d-flex justify-content-center mb-0 wow fadeInDown" data-wow-delay="0.3s">
                <li class="breadcrumb-item"><a class="text-white" href="{{path('app_home')}}">Home</a></li>
                <li class="breadcrumb-item active text-white-50">Shop</li>
                <li class="breadcrumb-item active text-primary">Products</li>
            </ol>
        </div>
    </div>
</div>

<div class="products-container">
    <!-- Products Header -->
    <div class="products-header">
        <h2>Discover Our Eco-Friendly Products</h2>
        <p>Browse our selection of sustainable and environmentally friendly products designed to help you reduce your ecological footprint while enjoying high-quality items.</p>
    </div>

    <!-- Filters Section -->
    <div class="products-filters">
        <div class="filter-group">
            <!-- Search Input -->
            <div class="search-input">
                <i class="ri-search-line"></i>
                <input type="text" id="searchField" placeholder="Search products...">
            </div>

            <!-- Category Filter -->
            <div class="filter-select">
                <select id="categoryFilter">
                    <option value="all">All Categories</option>
                    <option value="clothes">Clothes</option>
                    <option value="electronics">Electronics</option>
                    <option value="food">Food</option>
                    <option value="books">Books</option>
                    <option value="others">Others</option>
                </select>
            </div>

            <!-- Sort Filter -->
            <div class="filter-select">
                <select id="sortField">
                    <option value="name">Name (A-Z)</option>
                    <option value="price-low">Price (Low to High)</option>
                    <option value="price-high">Price (High to Low)</option>
                </select>
            </div>

            <!-- Filter Buttons -->
            <div class="filter-buttons">
                <button id="btnSort" class="btn-filter btn-primary">
                    <i class="ri-sort-asc"></i> Sort
                </button>
                <button id="btnToggleView" class="btn-filter btn-outline">
                    <i class="ri-list-check"></i> List View
                </button>
                <button id="btnClear" class="btn-filter btn-outline">
                    <i class="ri-refresh-line"></i> Reset
                </button>
            </div>
        </div>
    </div>

    <!-- Products Container - Will contain either grid or list view -->
    <div id="productsContainer">
        <!-- Grid View Container -->
        <div class="products-grid-view" id="gridViewContainer">
            {% for product in products %}
                <div class="grid-product-card" data-name="{{ product.nomP }}" data-price="{{ product.price }}" data-category="{{ product.categorie }}">
                    <div class="grid-product-image">
                        {% if product.image is not empty %}
                            <img src="http://localhost/img/{{ product.image }}" alt="{{ product.nomP }}">
                        {% else %}
                            <div class="grid-no-image">
                                <i class="ri-image-line"></i>
                            </div>
                        {% endif %}
                        <div class="grid-product-badges">
                            {% if product.isEcological %}
                                <span class="grid-badge grid-badge-eco"><i class="ri-leaf-line"></i> Eco</span>
                            {% endif %}

                            {% if product.stock > 10 %}
                                <span class="grid-badge grid-badge-stock"><i class="ri-stack-line"></i> In Stock</span>
                            {% elseif product.stock > 0 %}
                                <span class="grid-badge grid-badge-low"><i class="ri-error-warning-line"></i> Low Stock</span>
                            {% else %}
                                <span class="grid-badge grid-badge-out"><i class="ri-close-circle-line"></i> Out of Stock</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="grid-product-content">
                        <div class="grid-product-category">{{ product.categorie }}</div>
                        <h3 class="grid-product-title">{{ product.nomP }}</h3>
                        <p class="grid-product-description">{{ product.description }}</p>
                        <div class="grid-product-price-container">
                            <div class="grid-product-price">{{ product.price }}</div>
                            <button class="grid-qr-icon-btn" data-product-id="{{ product.id }}" data-product-name="{{ product.nomP }}">
                                <i class="ri-qr-code-line"></i>
                            </button>
                        </div>
                        <div class="grid-product-actions">
                            <button class="grid-btn-add-cart"
                                    data-id="{{ product.id }}"
                                    data-name="{{ product.nomP }}"
                                    data-price="{{ product.price }}"
                                    data-image="{{ product.image }}"
                                    data-category="{{ product.categorie }}"
                                    {% if product.stock <= 0 %} disabled {% endif %}
                            >
                                <i class="ri-shopping-cart-line"></i>
                                {% if product.stock <= 0 %}
                                    Out of stock
                                {% else %}
                                    Add to cart
                                {% endif %}
                            </button>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- List View Container -->
        <div class="products-list-view" id="listViewContainer" style="display: none;">
            {% for product in products %}
                <div class="list-product-card" data-name="{{ product.nomP }}" data-price="{{ product.price }}" data-category="{{ product.categorie }}">
                    <!-- Left: Product Image -->
                    <div class="list-product-image">
                        {% if product.image is not empty %}
                            <img src="http://localhost/img/{{ product.image }}" alt="{{ product.nomP }}">
                        {% else %}
                            <div class="list-no-image">
                                <i class="ri-image-line"></i>
                            </div>
                        {% endif %}
                        <div class="list-product-badges">
                            {% if product.isEcological %}
                                <span class="list-badge list-badge-eco"><i class="ri-leaf-line"></i> Eco</span>
                            {% endif %}

                            {% if product.stock > 10 %}
                                <span class="list-badge list-badge-stock"><i class="ri-stack-line"></i> In Stock</span>
                            {% elseif product.stock > 0 %}
                                <span class="list-badge list-badge-low"><i class="ri-error-warning-line"></i> Low Stock</span>
                            {% else %}
                                <span class="list-badge list-badge-out"><i class="ri-close-circle-line"></i> Out of Stock</span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Middle: Product Information -->
                    <div class="list-product-content">
                        <div class="list-product-header">
                            <div class="list-product-category">{{ product.categorie|upper }}</div>
                            <h3 class="list-product-title">{{ product.nomP }}</h3>
                        </div>
                        <p class="list-product-description">{{ product.description }}</p>
                        <div class="list-product-price">{{ product.price }}</div>
                        <div class="list-product-actions">
                            <button class="list-btn-add-cart"
                                    data-id="{{ product.id }}"
                                    data-name="{{ product.nomP }}"
                                    data-price="{{ product.price }}"
                                    data-image="{{ product.image }}"
                                    data-category="{{ product.categorie }}"
                                    {% if product.stock <= 0 %} disabled {% endif %}
                            >
                                <i class="ri-shopping-cart-line"></i>
                                {% if product.stock <= 0 %}
                                    Out of stock
                                {% else %}
                                    Add to cart
                                {% endif %}
                            </button>
                        </div>
                    </div>

                    <!-- Right: QR Code -->
                    <div class="list-product-qr">
                        <img src="{{ asset('uploads/qrcode/produit-' ~ product.id ~ '.png') }}" alt="QR code for {{ product.nomP }}">
                        <div class="list-qr-label">Scan for details</div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- QR Code Dialog -->
<div id="qrCodeDialog" class="qr-code-dialog">
    <div class="qr-dialog-content">
        <div class="qr-dialog-header">
            <h3 id="qrDialogTitle">QR Code</h3>
            <button id="closeQrDialog" class="close-qr-dialog">&times;</button>
        </div>
        <div class="qr-dialog-body">
            <div class="qr-image-container">
                <img id="qrCodeImage" src="" alt="QR Code">
            </div>
            <p class="qr-dialog-info">Scan this QR code to view product details on your mobile device</p>
        </div>
    </div>
</div>

<!-- Toast Notification CSS -->
<style>
    .toast-notification {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        display: flex;
        align-items: center;
        padding: 12px 16px;
        transform: translateY(100px);
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 9999;
    }

    .toast-notification.show {
        transform: translateY(0);
        opacity: 1;
    }

    .toast-icon {
        background-color: var(--primary-color);
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
    }

    .toast-content {
        flex: 1;
    }

    .toast-content p {
        margin: 0;
    }

    /* QR Code Dialog Styles */
    .qr-code-dialog {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        justify-content: center;
        align-items: center;
    }

    .qr-dialog-content {
        background-color: white;
        border-radius: 12px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        overflow: hidden;
        animation: scaleIn 0.3s ease;
    }

    .qr-dialog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: var(--primary-color);
        color: white;
    }

    .qr-dialog-header h3 {
        margin: 0;
        font-size: 1.2rem;
    }

    .close-qr-dialog {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0;
        line-height: 1;
    }

    .qr-dialog-body {
        padding: 20px;
        text-align: center;
    }

    .qr-image-container {
        margin: 10px auto 20px;
        padding: 15px;
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        display: inline-block;
    }

    .qr-image-container img {
        max-width: 200px;
        height: auto;
    }

    .qr-dialog-info {
        color: var(--text-medium);
        margin: 0;
        font-size: 0.9rem;
    }

    .qr-code-dialog.show {
        display: flex;
    }
</style>

<!-- Product Image Dialog -->
<div id="productImageDialog" class="product-image-dialog">
    <div class="product-image-dialog-content">
        <div class="product-image-dialog-close">
            <i class="ri-close-line"></i>
        </div>
        <img id="productImageDialogImg" class="product-image-dialog-img" src="" alt="Product Image">
    </div>
</div>

<!-- Import the external JS file -->
<script src="{{ asset('front/js/products.js') }}"></script>
{% endblock %}
