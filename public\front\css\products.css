/* Products Page Styling */
:root {
  --primary-color: #6BB748; /* Updated to match requested green color */
  --primary-dark: #5a9c3c;
  --primary-light: #83c566;
  --primary-very-light: #f0f7eb;
  --secondary-color: #17303B;
  --secondary-light: #2a4652;
  --text-dark: #333333;
  --text-medium: #555555;
  --text-light: #777777;
  --bg-light: #f8f9fa;
  --bg-white: #ffffff;
  --border-color: #e0e0e0;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
}

/* Products Container */
.products-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 3rem 1.5rem;
  position: relative;
}

/* Products Header */
.products-header {
  margin-bottom: 2.5rem;
  position: relative;
  padding-bottom: 1.5rem;
}

.products-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--primary-light));
  border-radius: 2px;
}

.products-header h2 {
  font-size: 2.25rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.products-header h2::before {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 40%;
  height: 2px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.products-header p {
  font-size: 1.15rem;
  color: var(--text-medium);
  max-width: 800px;
  line-height: 1.6;
}

/* Filters Section */
.products-filters {
  background-color: var(--bg-white);
  border-radius: var(--radius-md);
  padding: 1.75rem;
  margin-bottom: 2.5rem;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.products-filters:hover {
  box-shadow: var(--shadow-md);
}

.products-filters::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, var(--primary-color), var(--primary-dark));
  border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1.25rem;
  align-items: center;
}

.search-input {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input input {
  padding-left: 3rem;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  height: 48px;
  width: 100%;
  transition: all 0.3s ease;
  font-size: 1rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.search-input input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 208, 132, 0.15);
  outline: none;
}

.search-input i {
  position: absolute;
  left: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.search-input input:focus + i {
  color: var(--primary-dark);
}

.filter-select {
  min-width: 180px;
  position: relative;
}

.filter-select select {
  appearance: none;
  padding: 0.5rem 2.5rem 0.5rem 1.25rem;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  height: 48px;
  background-color: var(--bg-white);
  transition: all 0.3s ease;
  font-size: 1rem;
  width: 100%;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
  color: var(--text-medium);
}

.filter-select select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(0, 208, 132, 0.15);
  outline: none;
  color: var(--text-dark);
}

.filter-select::after {
  content: '\25BC';
  position: absolute;
  right: 1.25rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);
  pointer-events: none;
  font-size: 0.8rem;
  transition: transform 0.3s ease;
}

.filter-select:hover::after {
  transform: translateY(-50%) scale(1.1);
}

.filter-buttons {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.btn-filter {
  padding: 0 1.5rem;
  border-radius: var(--radius-sm);
  font-weight: 600;
  transition: all 0.3s ease;
  height: 48px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-filter::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
  z-index: -1;
}

.btn-filter:hover::before {
  left: 100%;
}

.btn-filter i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.btn-filter:hover i {
  transform: scale(1.1);
}

.btn-primary {
  background-color: var(--primary-color);
  border: 1px solid var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  color: white;
}

.btn-secondary:hover {
  background-color: var(--secondary-light);
  border-color: var(--secondary-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.btn-outline {
  background-color: var(--bg-white);
  border: 1px solid var(--border-color);
  color: var(--text-medium);
}

.btn-outline:hover {
  background-color: var(--bg-light);
  color: var(--primary-color);
  border-color: var(--primary-light);
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

/* ===== GRID VIEW STYLES ===== */
.products-grid-view {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

/* Grid Product Card */
.grid-product-card {
  background-color: var(--bg-white);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-normal);
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
  transform: translateZ(0); /* Hardware acceleration */
  backface-visibility: hidden; /* Smoother transitions */
  will-change: transform, box-shadow; /* Optimize for animations */
}

.grid-product-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
  border-color: rgba(0, 0, 0, 0.1);
  border-bottom: 3px solid var(--primary-color);
}

.grid-product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(107, 183, 72, 0.1) 0%, rgba(0, 0, 0, 0) 50%);
  opacity: 0;
  transition: opacity 0.5s ease;
  pointer-events: none;
  z-index: 1;
}

.grid-product-card:hover::before {
  opacity: 1;
}

.grid-product-image {
  position: relative;
  height: 280px;
  overflow: hidden;
  background-color: #f8f8f8;
  cursor: pointer; /* Add pointer cursor to indicate clickable */
}

.grid-product-image::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 30%;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.grid-product-image::before {
  content: '\ea0a'; /* Zoom icon from Remixicon */
  font-family: 'Remixicon';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.8);
  color: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 2;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.grid-product-card:hover .grid-product-image::after {
  opacity: 1;
}

.grid-product-card:hover .grid-product-image::before {
  opacity: 1;
}

.grid-product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.5s ease, filter 0.5s ease;
  filter: brightness(0.95);
}

.grid-product-card:hover .grid-product-image img {
  transform: scale(1.05);
  filter: brightness(1.05);
}

.grid-no-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
}

.grid-no-image i {
  font-size: 3.5rem;
  color: #ccc;
  opacity: 0.7;
}

.grid-product-badges {
  position: absolute;
  top: 15px;
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 2;
}

.grid-badge {
  padding: 0.4rem 1rem;
  border-radius: 0 50px 50px 0;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
}

.grid-badge i {
  font-size: 1rem;
}

.grid-badge-eco {
  background-color: var(--primary-color);
  color: white;
}

.grid-badge-stock {
  background-color: #3498db;
  color: white;
}

.grid-badge-low {
  background-color: #f39c12;
  color: white;
}

.grid-badge-out {
  background-color: #e74c3c;
  color: white;
}

.grid-product-content {
  padding: 1.75rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
  z-index: 2;
  background-color: var(--bg-white);
  border-top: 1px solid rgba(0, 0, 0, 0.03);
}

.grid-product-category {
  font-size: 0.85rem;
  color: var(--text-light);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
}

.grid-product-category::before {
  content: '';
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: var(--primary-color);
  border-radius: 50%;
}

.grid-product-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.75rem;
  line-height: 1.3;
  transition: color 0.3s ease;
  position: relative;
  padding-bottom: 0.5rem;
}

.grid-product-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.grid-product-card:hover .grid-product-title {
  color: var(--primary-color);
}

.grid-product-card:hover .grid-product-title::after {
  width: 60px;
}

.grid-product-description {
  color: var(--text-medium);
  margin-bottom: 1.25rem;
  flex-grow: 1;
  line-height: 1.6;
  font-size: 1.05rem;
}

.grid-product-price-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  position: relative;
  padding: 0.5rem 0;
}

.grid-product-price-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
  opacity: 0.5;
  border-radius: 3px;
}

.grid-product-price {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;
}

.grid-product-price::before {
  content: 'TND';
  font-size: 1rem;
  margin-right: 0.2rem;
  opacity: 0.7;
}

.grid-qr-icon-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: var(--primary-very-light);
  color: var(--primary-color);
  border: 1px solid var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.grid-qr-icon-btn i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.grid-qr-icon-btn:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.grid-qr-icon-btn:hover i {
  transform: scale(1.1);
}

.grid-product-actions {
  margin-top: auto;
}

.grid-btn-add-cart {
  width: 100%;
  padding: 1rem;
  border-radius: var(--radius-sm);
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  font-size: 1.05rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.grid-btn-add-cart::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.grid-btn-add-cart:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.grid-btn-add-cart:hover::before {
  left: 100%;
}

.grid-btn-add-cart:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.grid-btn-add-cart:disabled::before {
  display: none;
}

.grid-btn-add-cart i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.grid-btn-add-cart:hover i {
  transform: translateX(-3px);
}

/* Grid QR Code styles moved to price container section */

/* ===== LIST VIEW STYLES ===== */
.products-list-view {
  display: flex;
  flex-direction: column;
  gap: 3.5rem; /* Further increased spacing between list cards */
  margin-bottom: 2rem;
  padding: 1rem;
}

/* List Product Card */
.list-product-card {
  background-color: var(--bg-white);
  border-radius: var(--radius-md);
  overflow: hidden;
  box-shadow: var(--shadow-md); /* Increased shadow for better depth */
  transition: var(--transition-normal);
  display: flex;
  position: relative;
  border: 1px solid rgba(0, 0, 0, 0.05);
  min-height: 280px; /* Increased minimum height for larger images */
  margin-bottom: 1rem; /* Increased bottom margin */
}

.list-product-card:hover {
  transform: translateY(-5px); /* Increased lift effect */
  box-shadow: var(--shadow-lg);
  border-color: rgba(0, 0, 0, 0.1);
  border-left: 3px solid var(--primary-color);
}

/* ===== COMPLETELY REDESIGNED LIST VIEW IMAGE CONTAINER ===== */
.list-product-image {
  /* CRITICAL: Container must fill the entire left section */
  width: 400px;
  min-width: 400px;
  height: 100%;
  /* Remove ALL spacing that could prevent full coverage */
  padding: 0;
  margin: 0;
  /* Essential positioning */
  position: relative;
  overflow: hidden;
  /* Flex properties to prevent shrinking */
  flex-shrink: 0;
  flex-grow: 0;
  /* Visual styling */
  background: #f8f9fa;
  border-right: 1px solid var(--border-color);
  cursor: pointer;
  /* Force block display */
  display: block;
}

/* REDESIGNED: Image fills ENTIRE container */
.list-product-image img {
  /* ABSOLUTE positioning to cover EVERYTHING */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  /* Force 100% coverage */
  width: 100% !important;
  height: 100% !important;
  /* Object-fit to fill while maintaining aspect ratio */
  object-fit: cover;
  object-position: center;
  /* Remove any constraints */
  max-width: none;
  max-height: none;
  min-width: 100%;
  min-height: 100%;
  /* Styling */
  border: none;
  padding: 0;
  margin: 0;
  display: block;
  /* Smooth transitions */
  transition: transform 0.3s ease, filter 0.3s ease;
  /* Ensure it's above background */
  z-index: 1;
}

/* Hover effects */
.list-product-card:hover .list-product-image img {
  transform: scale(1.02);
  filter: brightness(1.1);
}

/* Fallback for missing images */
.list-no-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f5f5f5 0%, #e9e9e9 100%);
  z-index: 0;
}

.list-no-image i {
  font-size: 4rem;
  color: #ccc;
  opacity: 0.6;
}

/* Badges positioned over the full-size image */

.list-product-badges {
  position: absolute;
  top: 15px;
  left: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 20; /* Highest z-index to ensure badges appear above the full-size image */
}

.list-badge {
  padding: 0.4rem 1rem;
  border-radius: 0 50px 50px 0;
  font-size: 0.85rem;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.4rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
}

.list-badge i {
  font-size: 1rem;
}

.list-badge-eco {
  background-color: var(--primary-color);
  color: white;
}

.list-badge-stock {
  background-color: #3498db;
  color: white;
}

.list-badge-low {
  background-color: #f39c12;
  color: white;
}

.list-badge-out {
  background-color: #e74c3c;
  color: white;
}

/* Middle: Product Information */
.list-product-content {
  flex: 1;
  padding: 1.75rem 2rem;
  display: flex;
  flex-direction: column;
  position: relative;
}

.list-product-header {
  margin-bottom: 1rem;
}

.list-product-category {
  font-size: 0.85rem;
  color: var(--primary-color);
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 0.5rem;
  display: inline-flex;
  align-items: center;
  gap: 0.35rem;
  font-weight: 600;
}

.list-product-category::before {
  content: '•';
  color: var(--primary-color);
  font-size: 1.2rem;
  margin-right: 0.25rem;
}

.list-product-title {
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--text-dark);
  line-height: 1.3;
  transition: color 0.3s ease;
  position: relative;
  padding-bottom: 0.5rem;
  margin-top: 0.25rem;
}

.list-product-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
  transition: width 0.3s ease;
}

.list-product-card:hover .list-product-title {
  color: var(--primary-color);
}

.list-product-card:hover .list-product-title::after {
  width: 60px;
}

.list-product-description {
  color: var(--text-medium);
  margin-bottom: 1.5rem;
  flex-grow: 1;
  line-height: 1.6;
  font-size: 1.05rem;
}

.list-product-price {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  position: relative;
  padding: 0.5rem 0;
}

.list-product-price::before {
  content: 'TND';
  font-size: 1rem;
  margin-right: 0.2rem;
  opacity: 0.7;
}

.list-product-price::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
  opacity: 0.5;
  border-radius: 3px;
}

.list-product-actions {
  margin-top: auto;
}

.list-btn-add-cart {
  width: 100%;
  max-width: 250px;
  padding: 0.9rem;
  border-radius: var(--radius-sm);
  background-color: var(--primary-color);
  color: white;
  font-weight: 600;
  font-size: 1rem;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.list-btn-add-cart::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.list-btn-add-cart:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.list-btn-add-cart:hover::before {
  left: 100%;
}

.list-btn-add-cart:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.list-btn-add-cart:disabled::before {
  display: none;
}

.list-btn-add-cart i {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.list-btn-add-cart:hover i {
  transform: translateX(-3px);
}

/* Right: QR Code */
.list-product-qr {
  width: 180px;
  min-width: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  background-color: #f9f9f9;
  border-left: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.list-product-card:hover .list-product-qr {
  background-color: var(--primary-very-light);
}

.list-product-qr img {
  width: 140px;
  height: 140px;
  border-radius: 4px;
  padding: 0;
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.list-product-qr img:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.list-qr-label {
  margin-top: 0.75rem;
  font-size: 0.8rem;
  color: var(--text-medium);
  text-align: center;
}

/* Responsive Adjustments */
@media (max-width: 1200px) {
  .products-container {
    padding: 2.5rem 1.25rem;
  }

  .products-header h2 {
    font-size: 2rem;
  }

  /* Grid View Adjustments */
  .grid-product-card {
    transform: none;
  }

  .grid-product-card:hover {
    transform: translateY(-3px);
  }

  /* List View Adjustments */
  .list-product-card {
    min-height: 280px; /* Increased for larger images */
  }

  .list-product-image {
    width: 350px; /* Maintain large width */
    min-width: 350px;
    height: 100%;
    position: relative;
    overflow: hidden;
  }

  .list-product-image img {
    /* Ensure images still fill ENTIRE container at this breakpoint */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover;
    object-position: center;
  }

  .list-product-qr {
    width: 130px;
    min-width: 130px;
  }

  .list-product-qr img {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 992px) {
  .products-container {
    padding: 2rem 1rem;
  }

  .products-header {
    margin-bottom: 2rem;
  }

  .products-header h2 {
    font-size: 1.75rem;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input, .filter-select {
    width: 100%;
  }

  .filter-buttons {
    justify-content: space-between;
    width: 100%;
    margin-top: 0.5rem;
  }

  .btn-filter {
    flex: 1;
    padding: 0 1rem;
    font-size: 0.9rem;
  }

  .btn-filter i {
    font-size: 1.1rem;
  }

  /* Grid View Adjustments */
  .products-grid-view {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 1.25rem;
  }

  .grid-product-image {
    height: 200px;
  }

  .grid-product-content {
    padding: 1.5rem;
  }

  .grid-product-title {
    font-size: 1.25rem;
  }

  .grid-product-price {
    font-size: 1.4rem;
  }

  /* List View Adjustments */
  .list-product-card {
    flex-direction: row;
    flex-wrap: wrap;
  }

  .list-product-image {
    width: 300px; /* Maintain larger width */
    min-width: 300px;
    height: 100%;
    position: relative;
    overflow: hidden;
  }

  .list-product-image img {
    /* Ensure images still fill ENTIRE container at this breakpoint */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover;
    object-position: center;
  }

  .list-product-content {
    padding: 1.25rem;
  }

  .list-product-title {
    font-size: 1.4rem;
  }

  .list-product-qr {
    width: 120px;
    min-width: 120px;
  }

  .list-product-qr img {
    width: 90px;
    height: 90px;
  }
}

@media (max-width: 768px) {
  .products-container {
    padding: 1.5rem 0.75rem;
  }

  .products-header h2 {
    font-size: 1.5rem;
  }

  .products-header p {
    font-size: 1rem;
  }

  /* Grid View Adjustments */
  .products-grid-view {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 1rem;
  }

  .grid-product-image {
    height: 180px;
  }

  .grid-product-content {
    padding: 1.25rem;
  }

  .grid-product-title {
    font-size: 1.15rem;
    margin-bottom: 0.5rem;
  }

  .grid-product-description {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .grid-product-price {
    font-size: 1.3rem;
    margin-bottom: 1.25rem;
  }

  .grid-btn-add-cart {
    padding: 0.75rem;
    font-size: 0.9rem;
  }

  .grid-product-qr img {
    max-width: 80px;
  }

  /* List View Adjustments */
  .list-product-card {
    flex-direction: column;
    min-height: auto;
  }

  .list-product-image {
    width: 100%;
    height: 250px;
    min-width: 100%;
    border-right: none;
    border-bottom: 1px solid var(--border-color);
    /* Maintain absolute positioning for images */
    position: relative;
    overflow: hidden;
  }

  .list-product-image img {
    /* Ensure images still fill container at mobile breakpoint */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .list-product-content {
    width: 100%;
  }

  .list-product-qr {
    width: 100%;
    min-width: 100%;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 1rem;
    border-left: none;
    border-top: 1px solid var(--border-color);
  }

  .list-product-qr img {
    width: 80px;
    height: 80px;
    margin-right: 1rem;
  }

  .list-qr-label {
    margin-top: 0;
  }
}

@media (max-width: 576px) {
  .products-container {
    padding: 1.25rem 0.5rem;
  }

  .products-header {
    text-align: center;
    padding-bottom: 1.25rem;
  }

  .products-header::after {
    left: 50%;
    transform: translateX(-50%);
  }

  .products-header h2 {
    font-size: 1.4rem;
    display: block;
  }

  .products-header h2::before {
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
  }

  .products-header p {
    font-size: 0.95rem;
  }

  /* Grid View Adjustments */
  .products-grid-view {
    grid-template-columns: 1fr;
  }

  .grid-product-image {
    height: 200px;
  }

  /* List View Adjustments */
  .list-product-image {
    height: 220px;
    /* Maintain absolute positioning for images */
    position: relative;
    overflow: hidden;
  }

  .list-product-image img {
    /* Ensure images still fill container at smallest mobile breakpoint */
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .list-product-title {
    font-size: 1.3rem;
  }

  .list-product-description {
    font-size: 0.9rem;
  }

  .filter-buttons {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .btn-filter {
    font-size: 0.85rem;
    padding: 0 0.75rem;
    height: 42px;
  }
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Additional animations for transitions */
.slide-in-right {
  animation: slideInRight 0.4s ease forwards;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.4s ease forwards;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.scale-in {
  animation: scaleIn 0.4s ease forwards;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Product Image Dialog */
.product-image-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.85);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.product-image-dialog.active {
  opacity: 1;
  visibility: visible;
}

.product-image-dialog-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  transform: scale(0.9);
  transition: transform 0.3s ease;
}

.product-image-dialog.active .product-image-dialog-content {
  transform: scale(1);
}

.product-image-dialog-img {
  display: block;
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
}

.product-image-dialog-close {
  position: absolute;
  top: 15px;
  right: 15px;
  width: 36px;
  height: 36px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 10;
  transition: background-color 0.3s ease;
}

.product-image-dialog-close:hover {
  background-color: #f1f1f1;
}

.product-image-dialog-close i {
  font-size: 1.2rem;
  color: #333;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 3rem;
  margin-bottom: 1rem;
}
